import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/models/chats_count.dart';
import 'package:mevolvesupport/providers/chats/chat_data_state.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';
import 'package:mevolvesupport/utilities/nullable.dart';

/// **ChatDataProvider - Advanced Real-time Chat List Manager**
///
/// This provider implements a sophisticated chat list management system with:
/// - Real-time updates for displayed items only (performance optimized)
/// - Intelligent staleness detection to notify when list needs refresh
/// - Cursor-based pagination with smart totalCount adjustment
/// - Multi-status count tracking for tab badges
/// - Deleted user handling with placeholder names
///
/// ## 🏗️ ARCHITECTURE OVERVIEW
///
/// ### Data Flow Patterns
///
/// **1. Initial Load Flow**
/// ```
/// initialize() → _setupListeners() → _loadInitialData() → _setupDisplayedChatsListener()
///                                 ↓
///                          Parallel: _setupTotalCountsListener()
/// ```
/// - Fetches first page of chats + total count
/// - Sets up targeted real-time listeners
/// - Initializes staleness detection
///
/// **2. Real-time Update Flow**
/// ```
/// Firestore Change → listenChatUpdatesByUIDs() → Update in-place → Re-sort → Emit
///                                             ↓
///                                      Preserve totalCount
/// ```
/// - Only listens to currently displayed chat UIDs (efficient)
/// - Updates existing items without changing list structure
/// - Maintains sort order and pagination state
///
/// **3. Pagination Flow**
/// ```
/// loadMore() → Fetch from cursor → Filter duplicates → Append → Update hasMore
///            ↓
///         Smart totalCount adjustment if no more items available
/// ```
/// - Uses last displayed item's timestamp as cursor
/// - Handles database inconsistencies gracefully
///
/// **4. Staleness Detection Flow**
/// ```
/// Background Listener → Compare UID sets → Set hasNewUpdates flag
///                    ↓
///               Only triggers on actual content differences
/// ```
/// - Runs parallel staleness check with same count as displayed
/// - Ignores order changes, only detects content differences
///
/// ## 📊 STATE MANAGEMENT
///
/// ### Core State Properties
/// - **`chatUsers`**: Currently displayed chat list (paginated)
/// - **`totalCount`**: Real-time total count from database
/// - **`hasMore`**: Can load more items (displayed < total)
/// - **`hasNewUpdates`**: Content staleness flag
/// - **`currentStatus`**: Active chat status filter
/// - **`supportPersonFilter`**: Active support person filter
/// - **`totalCounts`**: Badge counts for all statuses
///
/// ### Real-time Listeners
/// - **Displayed Items**: `_displayedChatsListener` (UID-based)
/// - **Staleness Detection**: `_stalenessDetectionListener` (background)
/// - **Count Badges**: Individual listeners per status
///
/// ## 🎯 PUBLIC API
///
/// **Core Operations**
/// - `initialize(status, filter)` - Load initial data with filters
/// - `loadMore()` - Load next batch of chats
/// - `refresh()` - Reload fresh data, clear staleness
/// - `changeFilter(status, filter)` - Change filters and reload
///
/// ## ⚡ PERFORMANCE OPTIMIZATIONS
///
/// - **Targeted Updates**: Only listen to displayed UIDs
/// - **Cursor Pagination**: Efficient database queries
/// - **Smart Deduplication**: Prevents duplicate items
/// - **Stable Counts**: totalCount preserved during updates
/// - **Background Staleness**: Non-blocking freshness checks
class ChatDataProvider extends Cubit<ChatDataState> {
  ChatDataProvider({
    required DatabaseRepository databaseRepository,
  })  : _databaseRepository = databaseRepository,
        super(ChatDataState.initial());

  final DatabaseRepository _databaseRepository;

  // ============================================================================
  // PRIVATE STATE VARIABLES
  // ============================================================================

  // --- Real-time Stream Subscriptions ---

  /// Listener for displayed chat items updates (efficient UID-based targeting)
  StreamSubscription? _displayedChatsListener;

  /// Listener for staleness detection (background monitoring, non-blocking)
  StreamSubscription? _stalenessDetectionListener;

  /// Individual status count listeners for tab badge updates
  StreamSubscription<int>? _notRepliedCountListener;
  StreamSubscription<int>? _repliedCountListener;
  StreamSubscription<int>? _clarifyCountListener;
  StreamSubscription<int>? _resolvedCountListener;

  /// Track the support person filter used by count listeners
  String? _countListenersSupportPersonFilter;

  // --- Pagination & Cursor State ---

  /// Pagination cursor using localUpdatedAt (consistent with Firestore ordering)
  DateTime? _lastLocalDateTime;

  /// Items per page for initial load (balanced performance vs UX)
  static const int _itemsPerPage = 10;

  /// Batch size for loadMore operations (optimized for smooth scrolling)
  static const int _loadMoreBatchSize = 10;

  /// Maximum attempts for shortfall handling (prevents infinite loops)
  static const int _maxShortfallAttempts = 5;

  // --- Utility State ---

  /// Counter for generating unique placeholder names for deleted users
  int _deletedUserIdCounter = 1;

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  @override
  Future<void> close() {
    _displayedChatsListener?.cancel();
    _stalenessDetectionListener?.cancel();
    _notRepliedCountListener?.cancel();
    _repliedCountListener?.cancel();
    _clarifyCountListener?.cancel();
    _resolvedCountListener?.cancel();
    return super.close();
  }

  // ============================================================================
  // PUBLIC API METHODS
  // ============================================================================

  /// Initialize chat list with filters and set up real-time listeners
  ///
  /// This is the main entry point that:
  /// - Cleans up existing listeners
  /// - Resets pagination state
  /// - Loads initial data batch
  /// - Sets up targeted real-time updates
  /// - Initializes staleness detection
  void initialize({
    ChatStatus? status,
    String? supportPersonFilter,
  }) {
    Log.d(
      '🔄 ChatDataProvider: Initializing with status: ${status?.toReadableString() ?? "default"}, supportPerson: $supportPersonFilter',
    );

    // Store old support person filter to check if it changed
    final oldSupportPersonFilter = state.supportPersonFilter;

    Log.d('  🗑️ Cancelling old listeners...');
    _cancelListeners();
    Log.d('  🔄 Resetting pagination...');
    _resetPagination();

    Log.d('  🎨 Emitting loading state with new filters...');
    emit(
      ChatDataState.initial().copyWith(
        currentStatus: status,
        supportPersonFilter: Nullable(supportPersonFilter),
        totalCounts:
            state.totalCounts, // Preserve existing counts to prevent flicker
      ),
    );

    Log.d('  👂 Setting up new listeners...');
    _setupListeners();

    // Only setup count listeners if support person filter changed or if no listeners exist
    final supportPersonChanged = supportPersonFilter != _countListenersSupportPersonFilter;
    final noCountListeners = _notRepliedCountListener == null;
    if (supportPersonChanged || noCountListeners) {
      Log.d('  📊 Setting up count listeners (support person filter changed: $supportPersonChanged, no listeners: $noCountListeners)');
      _setupTotalCountsListener();
      _countListenersSupportPersonFilter = supportPersonFilter;
    } else {
      Log.d('  📊 Keeping existing count listeners (support person filter unchanged: ${supportPersonFilter})');
    }
  }

  /// Refresh entire chat list and clear staleness indicators
  ///
  /// This performs a full reset:
  /// - Clears hasNewUpdates flag
  /// - Re-initializes with current filters
  /// - Resets pagination to first page
  Future<void> refresh() async {
    Log.d('🔄 ChatDataProvider: Refreshing chats data');

    // Clear new updates flag when refreshing
    emit(state.copyWith(hasNewUpdates: false));

    // Re-initialize with current filters
    initialize(
      status: state.currentStatus,
      supportPersonFilter: state.supportPersonFilter,
    );
  }

  /// Change status/support person filters and reload data
  ///
  /// Efficiently handles filter changes by preserving unchanged filters
  /// and performing a clean re-initialization
  void changeFilter({
    ChatStatus? status,
    String? supportPersonFilter = '__NO_CHANGE__',
  }) {
    final newStatus = status ?? state.currentStatus;
    // Use supportPersonFilter if provided (including null for "All"), otherwise keep current
    final newSupportPerson = supportPersonFilter == '__NO_CHANGE__'
        ? state.supportPersonFilter
        : supportPersonFilter;

    Log.d('🎛️ ChatDataProvider: Changing filter');
    Log.d(
      '  🔄 OLD - status: ${state.currentStatus.toReadableString()}, supportPerson: ${state.supportPersonFilter}',
    );
    Log.d(
      '  ✨ NEW - status: ${newStatus.toReadableString()}, supportPerson: $newSupportPerson',
    );

    initialize(
      status: newStatus,
      supportPersonFilter: newSupportPerson,
    );
  }

  /// Force refresh chat data to ensure new messages are immediately visible
  ///
  /// This method is called when new messages are detected to ensure
  /// chat status changes are immediately reflected in the UI
  void forceRefreshForNewMessages() {
    Log.d('🔄 ChatDataProvider: Force refreshing for new messages');

    // Set hasNewUpdates to true to indicate fresh data is needed
    emit(state.copyWith(hasNewUpdates: true));

    // Trigger a refresh to get the latest data
    refresh();
  }

  // ============================================================================
  // PRIVATE SETUP & INITIALIZATION
  // ============================================================================

  /// Core setup method: loads initial data and configures real-time listeners
  ///
  /// Flow:
  /// 1. Load first page of chats + total count
  /// 2. Set up targeted listener for displayed items
  /// 3. Start staleness detection in background
  Future<void> _setupListeners() async {
    await _loadInitialData();

    Log.d(
      '👂 ChatDataProvider: Setting up listeners for status: ${state.currentStatus.toReadableString()}',
    );

    _setupDisplayedChatsListener();
  }

  /// Cancel chat-specific listeners while preserving count listeners
  ///
  /// Note: Total count listeners remain active across filter changes
  /// to maintain accurate tab badge counts
  void _cancelListeners() {
    _displayedChatsListener?.cancel();
    _displayedChatsListener = null;
    _stalenessDetectionListener?.cancel();
    _stalenessDetectionListener = null;
    // Don't cancel total counts listener here - it stays active
  }

  /// Reset pagination cursor and utility counters for clean state
  ///
  /// Called during initialization to ensure consistent starting state
  void _resetPagination() {
    _lastLocalDateTime = null;
    _deletedUserIdCounter = 1;
  }

  // ============================================================================
  // PRIVATE LISTENER SETUP - REAL-TIME UPDATES
  // ============================================================================

  /// Set up individual filtered count listeners for tab badges
  ///
  /// Uses listenFilteredChatsCount for each status to get accurate counts.
  /// Combines individual status counts into ChatsCount object for state.
  ///
  /// Why individual listeners?
  /// - More accurate than old global count document
  /// - Uses same filtering logic as main chat queries
  /// - Real-time updates without polling
  /// - Enhanced with message-level real-time updates for immediate status changes
  void _setupTotalCountsListener() {
    Log.d(
      '📊 ChatDataProvider: Setting up total counts listener using listenFilteredChatsCount with enhanced real-time updates',
    );

    // Cancel any existing listeners
    _notRepliedCountListener?.cancel();
    _repliedCountListener?.cancel();
    _clarifyCountListener?.cancel();
    _resolvedCountListener?.cancel();

    // Track current counts for each status - preserve existing counts to prevent flicker
    int notRepliedCount = state.totalCounts.notReplied;
    int repliedCount = state.totalCounts.replied;
    int clarifyCount = state.totalCounts.clarify;
    int resolvedCount = state.totalCounts.resolved;

    void emitUpdatedCounts() {
      final counts = ChatsCount(
        notReplied: notRepliedCount,
        replied: repliedCount,
        clarify: clarifyCount,
        resolved: resolvedCount,
      );

      Log.d('🔢 ChatDataProvider: Emitting updated total counts - '
          'NotReplied: ${counts.notReplied}, '
          'Clarify: ${counts.clarify}, '
          'Replied: ${counts.replied}, '
          'Resolved: ${counts.resolved}');

      emit(
        state.copyWith(
          totalCounts: counts,
        ),
      );
    }

    // Create individual real-time listeners for each chat status
    // Note: byFilter=null means count ALL support persons for tab badges
    // Enhanced with immediate refresh for notReplied to catch new messages faster
    _notRepliedCountListener = _databaseRepository
        .listenFilteredChatsCount(
      status: ChatStatus.notReplied,
      byFilter: null, // No filter = all support persons
    )
        .listen(
      (count) {
        Log.d('🔢 NotReplied count updated: $count');
        final previousCount = notRepliedCount;
        notRepliedCount = count;

        // If notReplied count increased, force refresh to show new messages immediately
        if (count > previousCount &&
            state.currentStatus == ChatStatus.notReplied) {
          Log.d(
            '📨 New message detected - forcing refresh for immediate visibility',
          );
          forceRefreshForNewMessages();
        }

        emitUpdatedCounts();
      },
      onError: (error) {
        Log.e(
          '❌ ChatDataProvider: Error listening to notReplied count: $error',
        );
      },
    );

    _repliedCountListener = _databaseRepository
        .listenFilteredChatsCount(
      status: ChatStatus.replied,
      byFilter: null, // No filter = all support persons
    )
        .listen(
      (count) {
        Log.d('🔢 Replied count updated: $count');
        final previousCount = repliedCount;
        repliedCount = count;

        // If replied count increased and we're viewing replied tab, refresh to show new replies
        // This helps with clarified -> replied transitions
        if (count > previousCount &&
            state.currentStatus == ChatStatus.replied) {
          Log.d(
            '💬 New reply detected - forcing refresh for clarified->replied transitions',
          );
          forceRefreshForNewMessages();
        }

        emitUpdatedCounts();
      },
      onError: (error) {
        Log.e('❌ ChatDataProvider: Error listening to replied count: $error');
      },
    );

    _clarifyCountListener = _databaseRepository
        .listenFilteredChatsCount(
      status: ChatStatus.clarify,
      byFilter: null, // No filter = all support persons
    )
        .listen(
      (count) {
        Log.d('🔢 Clarify count updated: $count');
        clarifyCount = count;
        emitUpdatedCounts();
      },
      onError: (error) {
        Log.e('❌ ChatDataProvider: Error listening to clarify count: $error');
      },
    );

    _resolvedCountListener = _databaseRepository
        .listenFilteredChatsCount(
      status: ChatStatus.resolved,
      byFilter: null, // No filter = all support persons
    )
        .listen(
      (count) {
        Log.d('🔢 Resolved count updated: $count');
        resolvedCount = count;
        emitUpdatedCounts();
      },
      onError: (error) {
        Log.e('❌ ChatDataProvider: Error listening to resolved count: $error');
      },
    );
  }

  // ============================================================================
  // PRIVATE DATA LOADING - INITIAL & PAGINATION
  // ============================================================================

  /// Load initial page of chats and total count (one-time fetch)
  ///
  /// This is a clean fetch operation that:
  /// - Gets first page of chats matching current filters
  /// - Fetches accurate total count for pagination
  /// - Processes deleted users with placeholder names
  /// - Sets up pagination cursor for future loadMore calls
  ///
  /// Note: No real-time listeners set up here - that's done separately
  Future<void> _loadInitialData() async {
    Log.d(
      '📥 ChatDataProvider: Loading initial chats data (no real-time updates)',
    );

    try {
      // Parallel fetch for optimal performance: chats + count
      final results = await Future.wait([
        _databaseRepository.fetchChats(
          status: state.currentStatus,
          limit: _itemsPerPage,
          byFilter: state.supportPersonFilter,
        ),
        _databaseRepository.fetchFilteredChatsCount(
          status: state.currentStatus,
          byFilter: state.supportPersonFilter,
        ),
      ]);

      final chats = results[0] as List<ChatUser>;
      final totalCount = results[1] as int;

      Log.d(
        '✅ ChatDataProvider: Loaded ${chats.length} initial chats, total count: $totalCount',
      );

      // Apply deleted user processing (placeholder names)
      final processedChats = _processDeletedUsers(chats);

      // Sort by timestamp to match database ordering (newest first)
      _sortChatsByTimestamp(processedChats);

      // Set pagination cursor to last item for future loadMore calls
      if (processedChats.isNotEmpty) {
        _lastLocalDateTime = processedChats.last.localUpdatedAt;
      }

      // Determine if more pages available by comparing counts
      final hasMoreData = processedChats.length < totalCount;

      emit(
        state.copyWith(
          chatUsers: processedChats,
          totalCount: totalCount,
          // Use real database count (only updated on initial load/refresh)
          isLoading: false,
          error: null,
          lastUpdate: DateTime.now(),
          hasMore: hasMoreData,
          // More pages available if we have fewer items than total
          hasNewUpdates:
              false, // Always false on initial load (no staleness yet)
        ),
      );
    } catch (error) {
      Log.e('❌ ChatDataProvider: Error loading initial chats: $error');
      emit(
        state.copyWith(
          isLoading: false,
          error: 'Failed to load chats: $error',
        ),
      );
    }
  }

  // ============================================================================
  // PRIVATE UTILITY & HELPER METHODS
  // ============================================================================

  /// Load more chats with advanced duplicate handling and cursor advancement
  ///
  /// **Pagination Strategy:**
  /// - Uses cursor-based pagination with `lastLocalDateTime`
  /// - Fetches batches of `_loadMoreBatchSize` items from database
  /// - Handles database inconsistencies and duplicate detection gracefully
  ///
  /// **Duplicate Handling Logic:**
  /// 1. **Initial Fetch**: Get next batch using current cursor position
  /// 2. **Process & Deduplicate**: Remove items already in displayed list
  /// 3. **Handle Shortfall**: If unique items < batch size AND we got full batch:
  ///    - Sort ALL processed items (including duplicates) by timestamp
  ///    - Advance cursor to last item's timestamp (ensures forward progress)
  ///    - Fetch additional batch and repeat until sufficient unique items
  /// 4. **End Detection**: Stop when partial batch received (< batch size)
  ///
  /// **Cursor Advancement Strategy:**
  /// - Critical insight: Sort ALL items (duplicates + unique) before advancing cursor
  /// - This ensures cursor always moves forward, even with 100% duplicate batches
  /// - Prevents infinite loops while maintaining database consistency
  ///
  /// **State Updates:**
  /// - Merge unique items with existing displayed list
  /// - Update `hasMore` based on partial batch detection
  /// - Adjust `totalCount` if database end reached
  /// - Refresh staleness detection to match new pagination state
  Future<void> loadMore() async {
    if (state.isLoadingMore || !state.hasMore) return;

    Log.d('📄 LoadMore: Loading next $_loadMoreBatchSize chats...');
    emit(state.copyWith(isLoadingMore: true));

    try {
      // Initial fetch
      var newChats = await _databaseRepository.fetchChats(
        status: state.currentStatus,
        lastLocalDateTime: _lastLocalDateTime,
        limit: _loadMoreBatchSize,
        byFilter: state.supportPersonFilter,
      );

      if (newChats.isEmpty) {
        // Handle empty case
        emit(
          state.copyWith(
            isLoadingMore: false,
            hasMore: false,
            totalCount: state.chatUsers.length,
          ),
        );
        return;
      }

      // Process and deduplicate
      var processedChats = _processDeletedUsers(newChats);
      final existingUids = state.chatUsers.map((c) => c.uid).toSet();
      var newUniqueChats =
          processedChats.where((c) => !existingUids.contains(c.uid)).toList();

      // Handle duplicate shortfall with cursor advancement (max attempts for safety)
      int shortfallAttempts = 0;

      while (newUniqueChats.length < _loadMoreBatchSize &&
          newChats.length == _loadMoreBatchSize &&
          shortfallAttempts < _maxShortfallAttempts) {
        shortfallAttempts++;
        Log.d(
          '🔄 Shortfall attempt $shortfallAttempts/$_maxShortfallAttempts: ${newUniqueChats.length} < $_loadMoreBatchSize, fetching more...',
        );

        // Sort ALL processed items (including duplicates) to advance cursor
        _sortChatsByTimestamp(processedChats);

        // Update cursor from sorted batch (advances even with duplicates)
        if (processedChats.isNotEmpty) {
          _lastLocalDateTime = processedChats.last.localUpdatedAt;
          Log.d('🎯 Cursor advanced to: $_lastLocalDateTime');
        }

        // Fetch next batch
        final additionalBatch = await _databaseRepository.fetchChats(
          status: state.currentStatus,
          lastLocalDateTime: _lastLocalDateTime,
          limit: _loadMoreBatchSize,
          byFilter: state.supportPersonFilter,
        );

        // Check for end conditions
        if (additionalBatch.isEmpty ||
            additionalBatch.length < _loadMoreBatchSize) {
          Log.d('🏁 Reached database end: ${additionalBatch.length} items');
          newChats = additionalBatch;
          break;
        }

        // Process additional batch
        final processedAdditional = _processDeletedUsers(additionalBatch);
        final allUids = [...existingUids, ...newUniqueChats.map((c) => c.uid)];
        final uniqueFromAdditional =
            processedAdditional.where((c) => !allUids.contains(c.uid)).toList();

        // Add to collections
        newUniqueChats.addAll(uniqueFromAdditional);
        processedChats = [...processedChats, ...processedAdditional];
        newChats = additionalBatch;

        Log.d(
          '📊 Additional: ${processedAdditional.length} processed, ${uniqueFromAdditional.length} unique',
        );
      }

      // Safety check: warn if we hit the attempt limit
      if (shortfallAttempts >= _maxShortfallAttempts) {
        Log.w(
            '⚠️ LoadMore: Reached max shortfall attempts ($_maxShortfallAttempts). '
            'Proceeding with ${newUniqueChats.length} unique items to prevent infinite loop.');
      }

      // Final merge and sort
      final allChats = [...state.chatUsers, ...newUniqueChats];
      _sortChatsByTimestamp(allChats);

      // Update final cursor from displayed items
      if (allChats.isNotEmpty) {
        _lastLocalDateTime = allChats.last.localUpdatedAt;
      }

      // Determine hasMore
      final gotPartialBatch = newChats.length < _loadMoreBatchSize;
      final reachedExpectedTotal = allChats.length >= state.totalCount;
      final hasMoreData = !gotPartialBatch && !reachedExpectedTotal;
      final adjustedTotalCount =
          gotPartialBatch ? allChats.length : state.totalCount;

      Log.d('✅ LoadMore complete: ${newUniqueChats.length} unique items added');

      emit(
        state.copyWith(
          chatUsers: allChats,
          isLoadingMore: false,
          hasMore: hasMoreData,
          totalCount: adjustedTotalCount,
          lastUpdate: DateTime.now(),
        ),
      );

      // Update listeners
      _updateStalenessListenerCount();
      _displayedChatsListener?.cancel();
      _setupDisplayedChatsListener();
    } catch (error) {
      Log.e('❌ LoadMore error: $error');
      emit(
        state.copyWith(
          isLoadingMore: false,
          error: 'Failed to load more chats: ${error.toString()}',
        ),
      );
    }
  }

  /// Process deleted users by replacing names with placeholders
  ///
  /// Handles users marked for deletion by giving them recognizable
  /// placeholder names with incrementing counter for uniqueness
  List<ChatUser> _processDeletedUsers(List<ChatUser> chats) {
    final processedChats = <ChatUser>[];

    for (var chat in chats) {
      if (chat.userDeletedStatus != null &&
          (chat.userDeletedStatus == UserDeletedStatus.remove_a ||
              chat.userDeletedStatus == UserDeletedStatus.remove_u)) {
        final newChat = chat.copyWith(
          uname:
              'Deleted user ${chat.uname.isNotEmpty ? chat.uname : _deletedUserIdCounter}',
        );
        _deletedUserIdCounter++;
        processedChats.add(newChat);
      } else {
        processedChats.add(chat);
      }
    }

    return processedChats;
  }

  /// Sort chats by localUpdatedAt timestamp (newest first)
  ///
  /// Maintains consistency with database ordering for predictable pagination
  void _sortChatsByTimestamp(List<ChatUser> chats) {
    chats.sort((a, b) {
      final aTime = a.localUpdatedAt;
      final bTime = b.localUpdatedAt;
      if (aTime == null && bTime == null) return 0;
      if (aTime == null) return 1;
      if (bTime == null) return -1;
      return bTime.compareTo(aTime); // Most recent first (descending)
    });
  }

  /// Set up background staleness detection listener
  ///
  /// This runs a parallel query with the same count as displayed items
  /// to detect when new content becomes available. Only triggers when
  /// the actual content (UIDs) differs, not just order changes.
  void _setupStalenessDetectionListener() {
    Log.d(
      '📡 Setting up real-time list listener for staleness detection - status: ${state.currentStatus.toReadableString()}',
    );

    // Use current displayed count or default to initial page size
    final initialCount =
        state.chatUsers.isNotEmpty ? state.chatUsers.length : _itemsPerPage;

    _stalenessDetectionListener = _databaseRepository
        .listenChatUpdates(
      status: state.currentStatus,
      count: initialCount, // Start with initial count
      byFilter: state.supportPersonFilter,
    )
        .listen(
      (realtimeChats) {
        Log.d(
          '🔍 Real-time list: Received ${realtimeChats.length} chats (expected: ${state.chatUsers.length})',
        );

        // Only run staleness check if we have items to compare against
        if (state.chatUsers.isNotEmpty) {
          // Apply same processing pipeline as displayed data for fair comparison
          final processedRealtimeChats = _processDeletedUsers(realtimeChats);
          _sortChatsByTimestamp(processedRealtimeChats);

          // Compare content by UID sets (order differences don't matter)
          final currentUIDs = state.chatUsers.map((c) => c.uid).toSet();
          final realtimeUIDs = processedRealtimeChats.map((c) => c.uid).toSet();

          final hasChanges =
              !(const SetEquality().equals(currentUIDs, realtimeUIDs));

          Log.d(
            '🔍 UID Comparison - Current: ${currentUIDs.length} items, Realtime: ${realtimeUIDs.length} items, HasChanges: $hasChanges',
          );

          if (hasChanges) {
            Log.d('🚨 Staleness detected! Setting hasNewUpdates to true');
            emit(
              state.copyWith(
                hasNewUpdates: true,
                lastUpdate: DateTime.now(),
              ),
            );
          }
        }
      },
      onError: (error) {
        Log.e('❌ ChatDataProvider: Real-time list listener error: $error');
      },
    );
  }

  /// Update staleness listener to match current pagination state
  ///
  /// Called after loadMore to ensure staleness detection uses the
  /// same item count as currently displayed for accurate comparison
  void _updateStalenessListenerCount() {
    if (state.chatUsers.isNotEmpty) {
      Log.d(
        '🔄 Setting up real-time listener to match displayed items: ${state.chatUsers.length}',
      );

      // Restart listener with updated count to match displayed items
      _stalenessDetectionListener?.cancel();
      _setupStalenessDetectionListener();
    }
  }

  /// Set up targeted real-time listener for currently displayed chats
  ///
  /// This is the core efficiency feature: instead of listening to ALL chats,
  /// we only listen to the specific UIDs currently displayed. This provides:
  /// - Real-time updates for visible items only
  /// - Minimal bandwidth and processing
  /// - In-place updates without list reconstruction
  /// - Automatic removal of chats that no longer match the current filter
  void _setupDisplayedChatsListener() {
    if (state.chatUsers.isEmpty) {
      Log.d('📡 No displayed items to listen for updates');
      return;
    }

    final displayedUIDs = state.chatUsers.map((c) => c.uid).toList();
    Log.d(
      '📡 Setting up data listener for ${displayedUIDs.length} displayed UIDs: ${displayedUIDs.take(3).join(", ")}...',
    );

    _displayedChatsListener =
        _databaseRepository.listenChatUpdatesByUIDs(displayedUIDs).listen(
      (updatedChats) async {
        Log.d(
          '📡 Real-time data: Received ${updatedChats.length} updated chats for displayed UIDs',
        );

        if (updatedChats.isNotEmpty) {
          // Work on copy to avoid mutating state during processing
          final currentChats = List<ChatUser>.from(state.chatUsers);

          // Apply same deleted user processing as initial load
          final processedUpdatedChats = _processDeletedUsers(updatedChats);

          // Find and update matching items in displayed list
          bool hasChanges = false;
          bool hasStatusChanges = false;
          for (final updatedChat in processedUpdatedChats) {
            final existingIndex =
                currentChats.indexWhere((c) => c.uid == updatedChat.uid);
            if (existingIndex != -1) {
              final oldChat = currentChats[existingIndex];

              // Check if the chat status no longer matches the current tab filter
              // Enhanced logic to handle clarified -> replied transitions correctly
              bool shouldRemove = false;

              if (state.currentStatus == ChatStatus.clarify) {
                // For clarify tab: show chats with clarify=true AND status!=resolved
                // Resolved chats should stay in resolved tab even if clarified
                shouldRemove = updatedChat.clarify != true ||
                    updatedChat.status == ChatStatus.resolved;
              } else if (state.currentStatus == ChatStatus.replied) {
                // For replied tab, accept chats that are replied OR were clarified but now replied
                // This fixes the issue where clarified chats don't move to replied after admin response
                shouldRemove = updatedChat.status != ChatStatus.replied;
              } else if (state.currentStatus == ChatStatus.notReplied) {
                // For not replied tab, exclude replied chats and clarified chats
                shouldRemove = updatedChat.status != ChatStatus.notReplied ||
                    updatedChat.clarify == true;
              } else if (state.currentStatus == ChatStatus.resolved) {
                // For resolved tab: show chats with status=resolved (regardless of clarify flag)
                shouldRemove = updatedChat.status != ChatStatus.resolved;
              } else {
                // For other tabs, check status and exclude clarified
                shouldRemove = updatedChat.status != state.currentStatus ||
                    updatedChat.clarify == true;
              }

              if (shouldRemove) {
                Log.d(
                  '📡 Real-time data: Removing chat ${updatedChat.uid} - status changed from ${oldChat.status} to ${updatedChat.status}, clarify: ${updatedChat.clarify}',
                );
                currentChats.removeAt(existingIndex);
                hasChanges = true;
                hasStatusChanges = true;
              } else {
                // Replace existing item with updated data if still matches filter
                currentChats[existingIndex] = updatedChat;
                hasChanges = true;
                Log.d('📡 Updated chat item: ${updatedChat.uid}');
              }
            }
          }

          if (hasChanges) {
            // Re-sort to maintain timestamp ordering after updates
            _sortChatsByTimestamp(currentChats);

            // Keep pagination cursor in sync with displayed items
            if (currentChats.isNotEmpty) {
              _lastLocalDateTime = currentChats.last.localUpdatedAt;
            }

            if (hasStatusChanges) {
              Log.d(
                '📡 Real-time data: Updated displayed items, totalCount: ${state.totalCount} → ${currentChats.length}, tab count: ${_getTabCount(state.totalCounts, state.currentStatus)} → ${currentChats.length}',
              );
            } else {
              Log.d(
                '📡 Real-time data: Updated displayed items, keeping existing counts',
              );
            }

            // Update totalCount when real-time filtering removes chats due to status changes
            final updatedTotalCount =
                hasStatusChanges ? currentChats.length : state.totalCount;

            // Also update the specific tab count in totalCounts for tab badges
            ChatsCount updatedTotalCounts = state.totalCounts;
            if (hasStatusChanges) {
              switch (state.currentStatus) {
                case ChatStatus.notReplied:
                  updatedTotalCounts = state.totalCounts
                      .copyWith(notReplied: currentChats.length);
                  break;
                case ChatStatus.clarify:
                  updatedTotalCounts =
                      state.totalCounts.copyWith(clarify: currentChats.length);
                  break;
                case ChatStatus.replied:
                  updatedTotalCounts =
                      state.totalCounts.copyWith(replied: currentChats.length);
                  break;
                case ChatStatus.resolved:
                  updatedTotalCounts =
                      state.totalCounts.copyWith(resolved: currentChats.length);
                  break;
                case ChatStatus.none:
                  // No specific count to update for 'none' status
                  break;
              }
            }

            emit(
              state.copyWith(
                chatUsers: currentChats,
                totalCount: updatedTotalCount,
                // Update count when chats are removed by status changes
                totalCounts: updatedTotalCounts,
                // Update tab badge counts
                lastUpdate: DateTime.now(),
                // Preserve hasMore state during real-time updates
              ),
            );

            // If chats were removed due to status changes, update listeners
            if (hasStatusChanges) {
              Log.d(
                '📡 Real-time data: Status changes detected, updating listeners',
              );

              // Restart staleness detection to account for the new list size
              _stalenessDetectionListener?.cancel();
              _setupStalenessDetectionListener();

              // Restart displayed chats listener with updated UIDs
              _displayedChatsListener?.cancel();
              _setupDisplayedChatsListener();
            }
          } else {
            Log.d(
              '📡 Real-time data: No matching items found in displayed list',
            );
          }
        }
      },
      onError: (error) {
        Log.e('❌ ChatDataProvider: Data listener error: $error');
        emit(
          state.copyWith(
            error: 'Failed to update chat data: $error',
          ),
        );
      },
    );
  }

  /// Helper method to get the count for a specific status from totalCounts
  int _getTabCount(ChatsCount totalCounts, ChatStatus status) {
    switch (status) {
      case ChatStatus.notReplied:
        return totalCounts.notReplied;
      case ChatStatus.clarify:
        return totalCounts.clarify;
      case ChatStatus.replied:
        return totalCounts.replied;
      case ChatStatus.resolved:
        return totalCounts.resolved;
      case ChatStatus.none:
        return 0; // Return 0 for 'none' status
    }
  }
}
